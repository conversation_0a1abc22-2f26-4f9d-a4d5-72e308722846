[pytest]
python_files = tests.py

[tox]
envlist = test,typing

[testenv]
deps = 
    pytest
    pytest-cov
commands = pytest --cov --cov-branch --cov-report=xml

[testenv:typing]
skip_install = true
deps = 
    mypy
commands = mypy --check-untyped-defs configat check_typing.py

[testenv:coverage-html]
depends = test
deps = 
    coverage
commands = coverage html

[testenv:docdev]
deps = 
    sphinx
    sphinx-rtd-theme
    sphinx-autobuild
commands = sphinx-autobuild docs docs/_build