ConfigAt Documentation
======================

.. image:: https://codecov.io/gh/Dog-Egg/configat/graph/badge.svg?token=GmvbsZ2dBW
   :target: https://codecov.io/gh/Dog-Egg/configat
   :alt: Code Coverage

ConfigAt is a simple recursive configuration loader for Python that supports multiple data sources and formats through a flexible loader system.

Features
--------

* **Recursive Loading**: Load configurations from multiple sources recursively
* **Multiple Loaders**: Built-in support for environment variables, files, JSON, and more
* **Series Loaders**: Chain multiple loaders together for complex data transformations
* **Type Casting**: Automatic type conversion with built-in and custom casters
* **Extensible**: Easy to add custom loaders for specific use cases
* **Simple Syntax**: Clean and intuitive loader syntax

Quick Start
-----------

Install ConfigAt:

.. code-block:: bash

   pip install git+https://github.com/Dog-Egg/configat.git

Basic usage:

.. code-block:: python

   import configat

   # Load from environment variable
   DB_PASSWORD = configat.resolve("@env:DB_PASSWORD")
   
   # Load from file with automatic whitespace stripping
   SECRET = configat.resolve("@file-strip:/path/to/secret.txt")
   
   # Load JSON data with type casting
   PORT = configat.resolve("@env:PORT", cast=int)

Table of Contents
-----------------

.. toctree::
   :maxdepth: 2
   :caption: User Guide

   installation
   usage
   loaders
   type_casting
   advanced

.. toctree::
   :maxdepth: 2
   :caption: API Reference

   api/modules

Indices and tables
==================

* :ref:`genindex`
* :ref:`modindex`
* :ref:`search`
