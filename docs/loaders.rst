Built-in Loaders
================

ConfigAt comes with several built-in loaders that cover common configuration loading scenarios. This section provides detailed documentation for each loader.

Environment Loader (``env``)
-----------------------------

The environment loader retrieves values from environment variables.

**Syntax:** ``@env:<variable_name>``

**Parameters:**

* ``variable_name`` - The name of the environment variable to load

**Examples:**

.. code-block:: python

   import os
   import configat
   
   # Set environment variables
   os.environ["DATABASE_URL"] = "postgresql://localhost:5432/mydb"
   os.environ["DEBUG"] = "true"
   
   # Load environment variables
   db_url = configat.resolve("@env:DATABASE_URL")
   debug_mode = configat.resolve("@env:DEBUG")
   
   print(db_url)    # Output: postgresql://localhost:5432/mydb
   print(debug_mode)  # Output: true

**Error Handling:**

Raises ``NotFoundError`` if the environment variable does not exist:

.. code-block:: python

   from configat.exceptions import NotFoundError
   
   try:
       value = configat.resolve("@env:NON_EXISTENT_VAR")
   except NotFoundError as e:
       print(f"Environment variable not found: {e}")

**Use Cases:**

* Loading database connection strings
* Configuration flags and feature toggles
* API keys and secrets
* Service endpoints and URLs

File Loader (``file``)
----------------------

The file loader reads content from files on the filesystem.

**Syntax:** ``@file:<file_path>``

**Parameters:**

* ``file_path`` - The path to the file to read (absolute or relative)

**Examples:**

.. code-block:: python

   import configat
   
   # Load content from a file
   content = configat.resolve("@file:/path/to/config.txt")
   
   # Load relative to current directory
   local_config = configat.resolve("@file:./config/settings.txt")
   
   # Load binary content (returned as string)
   data = configat.resolve("@file:/path/to/data.bin")

**Error Handling:**

Raises ``NotFoundError`` if the file does not exist or cannot be read:

.. code-block:: python

   from configat.exceptions import NotFoundError
   
   try:
       content = configat.resolve("@file:/non/existent/file.txt")
   except NotFoundError as e:
       print(f"File not found: {e}")

**Use Cases:**

* Loading configuration files
* Reading secrets from mounted volumes
* Loading templates or static content
* Reading Docker secrets
* Loading SSL certificates or keys

Strip Loader (``strip``)
------------------------

The strip loader removes leading and trailing whitespace from strings.

**Syntax:** ``@strip:<input_string>``

**Parameters:**

* ``input_string`` - The string to strip whitespace from

**Examples:**

.. code-block:: python

   import configat
   
   # Strip whitespace from a string
   clean = configat.resolve("@strip:  hello world  ")
   print(f"'{clean}'")  # Output: 'hello world'
   
   # Often used with other loaders
   # If config.txt contains "secret_value\n"
   secret = configat.resolve("@file-strip:/path/to/config.txt")
   print(f"'{secret}'")  # Output: 'secret_value'

**Behavior:**

* Removes spaces, tabs, newlines, and other whitespace characters
* Equivalent to Python's ``str.strip()`` method
* Returns the original value if it's not a string

**Use Cases:**

* Cleaning file content that may have trailing newlines
* Processing environment variables with accidental whitespace
* Normalizing user input
* Preparing values for further processing

JSON Loader (``json``)
----------------------

The JSON loader parses JSON strings into Python objects.

**Syntax:** ``@json:<json_string>``

**Parameters:**

* ``json_string`` - A valid JSON string to parse

**Examples:**

.. code-block:: python

   import configat
   
   # Parse simple JSON
   config = configat.resolve('@json:{"host": "localhost", "port": 8080}')
   print(config)  # Output: {'host': 'localhost', 'port': 8080}
   
   # Parse JSON array
   items = configat.resolve('@json:[1, 2, 3, "hello"]')
   print(items)  # Output: [1, 2, 3, 'hello']
   
   # Parse JSON from environment variable
   import os
   os.environ["CONFIG_JSON"] = '{"debug": true, "workers": 4}'
   config = configat.resolve("@env-json:CONFIG_JSON")
   print(config)  # Output: {'debug': True, 'workers': 4}

**Error Handling:**

Raises ``json.JSONDecodeError`` for invalid JSON:

.. code-block:: python

   import json
   
   try:
       config = configat.resolve('@json:{invalid json}')
   except json.JSONDecodeError as e:
       print(f"Invalid JSON: {e}")

**Use Cases:**

* Loading complex configuration objects
* Parsing JSON from environment variables
* Processing API responses stored as strings
* Loading structured data from files

Series Loaders
--------------

Loaders can be chained together using the ``-`` separator to create powerful data transformation pipelines.

**Syntax:** ``@loader1-loader2-loader3:<input>``

Common Combinations
~~~~~~~~~~~~~~~~~~~

**File + Strip (``@file-strip``)**

Load file content and remove whitespace:

.. code-block:: python

   # If secret.txt contains "my_secret_key\n"
   secret = configat.resolve("@file-strip:/path/to/secret.txt")
   print(f"'{secret}'")  # Output: 'my_secret_key'

**Environment + JSON (``@env-json``)**

Load JSON from environment variable:

.. code-block:: python

   import os
   os.environ["APP_CONFIG"] = '{"name": "myapp", "version": "1.0"}'
   
   config = configat.resolve("@env-json:APP_CONFIG")
   print(config["name"])  # Output: myapp

**Environment + File + Strip (``@env-file-strip``)**

Load file path from environment, then load and strip file content:

.. code-block:: python

   import os
   os.environ["SECRET_FILE"] = "/run/secrets/api_key"
   # Assume the file contains "abc123\n"
   
   api_key = configat.resolve("@env-file-strip:SECRET_FILE")
   print(f"'{api_key}'")  # Output: 'abc123'

**Environment + File + JSON (``@env-file-json``)**

Load JSON file path from environment:

.. code-block:: python

   import os
   os.environ["CONFIG_FILE"] = "/path/to/config.json"
   
   config = configat.resolve("@env-file-json:CONFIG_FILE")
   print(config)  # Parsed JSON content

Execution Order
~~~~~~~~~~~~~~~

Loaders are executed from left to right:

.. code-block:: python

   # @env-file-strip:CONFIG_PATH
   # 1. env loader: load CONFIG_PATH environment variable
   # 2. file loader: load content from the file path
   # 3. strip loader: remove whitespace from content

Best Practices
--------------

1. **Use appropriate loaders**: Choose the right loader for your data source
2. **Chain loaders wisely**: Use series loaders to transform data step by step
3. **Handle errors**: Always consider what happens when loaders fail
4. **Use defaults**: Provide sensible default values for optional configuration
5. **Document your patterns**: Make it clear what each loader expression does

Next Steps
----------

* Learn about :doc:`type_casting` for converting loaded values
* Explore :doc:`advanced` for creating custom loaders
* Check the :doc:`api/modules` for detailed API documentation
