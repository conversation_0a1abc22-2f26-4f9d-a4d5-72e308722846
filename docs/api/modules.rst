API Reference
=============

This section provides detailed API documentation for all ConfigAt modules, classes, and functions.

Main Module
-----------

.. automodule:: configat
   :members:
   :undoc-members:
   :show-inheritance:

Core Classes and Functions
--------------------------

.. automodule:: configat.main
   :members:
   :undoc-members:
   :show-inheritance:

Built-in Loaders
----------------

.. automodule:: configat.loaders
   :members:
   :undoc-members:
   :show-inheritance:

Type Casting
-------------

.. automodule:: configat.casting
   :members:
   :undoc-members:
   :show-inheritance:

Exceptions
----------

.. automodule:: configat.exceptions
   :members:
   :undoc-members:
   :show-inheritance:

Function Reference
------------------

resolve
~~~~~~~

.. autofunction:: configat.resolve

The main function for resolving configuration values. This function supports multiple overloads for different use cases:

**Basic usage:**

.. code-block:: python

   value = configat.resolve("@env:MY_VAR")

**With default value:**

.. code-block:: python

   value = configat.resolve("@env:MY_VAR", default="fallback")

**With type casting:**

.. code-block:: python

   value = configat.resolve("@env:MY_VAR", cast=int)

**With both default and casting:**

.. code-block:: python

   value = configat.resolve("@env:MY_VAR", default="0", cast=int)

Class Reference
---------------

ConfigAt
~~~~~~~~

.. autoclass:: configat.main.ConfigAt
   :members:
   :undoc-members:
   :show-inheritance:

The main ConfigAt class that manages loaders and resolves configuration expressions.

**Methods:**

* ``add_loader(name, func)`` - Register a custom loader
* ``resolve(expr, default=None, cast=None)`` - Resolve a configuration expression

**Example:**

.. code-block:: python

   from configat.main import ConfigAt
   
   # Create a custom ConfigAt instance
   config = ConfigAt()
   
   # Add custom loader
   def my_loader(value):
       return f"custom:{value}"
   
   config.add_loader("custom", my_loader)
   
   # Use the custom loader
   result = config.resolve("@custom:test")
   print(result)  # Output: custom:test

Loader Functions
----------------

env_loader
~~~~~~~~~~

.. autofunction:: configat.loaders.env_loader

Loads values from environment variables.

**Parameters:**

* ``name`` (str) - The environment variable name

**Returns:**

* str - The environment variable value

**Raises:**

* ``NotFoundError`` - If the environment variable doesn't exist

file_loader
~~~~~~~~~~~

.. autofunction:: configat.loaders.file_loader

Loads content from files.

**Parameters:**

* ``path`` (str) - The file path to read

**Returns:**

* str - The file content

**Raises:**

* ``NotFoundError`` - If the file doesn't exist or can't be read

strip_loader
~~~~~~~~~~~~

.. autofunction:: configat.loaders.strip_loader

Strips whitespace from strings.

**Parameters:**

* ``text`` (str) - The text to strip

**Returns:**

* str - The stripped text

json_loader
~~~~~~~~~~~

.. autofunction:: configat.loaders.json_loader

Parses JSON strings.

**Parameters:**

* ``text`` (str) - The JSON string to parse

**Returns:**

* Any - The parsed JSON object

**Raises:**

* ``json.JSONDecodeError`` - If the JSON is invalid

Type Casting Functions
----------------------

boolean
~~~~~~~

.. autofunction:: configat.casting.boolean

Converts various representations to boolean values.

**Supported truth values:**

* "True", "true", "1", 1, "Yes", "yes"

**Supported false values:**

* "False", "false", "0", 0, "No", "no"

**Parameters:**

* ``value`` - The value to convert

**Returns:**

* bool - The boolean value

**Raises:**

* ``ValueError`` - If the value cannot be converted to boolean

Exception Classes
-----------------

NotFoundError
~~~~~~~~~~~~~

.. autoexception:: configat.exceptions.NotFoundError

Raised when a requested resource (environment variable, file, etc.) is not found.

This exception is typically raised by loaders when they cannot find the requested resource.

**Example:**

.. code-block:: python

   from configat.exceptions import NotFoundError
   import configat
   
   try:
       value = configat.resolve("@env:NON_EXISTENT")
   except NotFoundError as e:
       print(f"Resource not found: {e}")

Constants and Variables
-----------------------

TRUTH_VALUES
~~~~~~~~~~~~

.. autodata:: configat.casting.TRUTH_VALUES

Set of values that are considered "true" by the boolean caster.

FALSE_VALUES
~~~~~~~~~~~~

.. autodata:: configat.casting.FALSE_VALUES

Set of values that are considered "false" by the boolean caster.
