Installation
============

Requirements
------------

ConfigAt requires Python 3.8 or later. It has no external dependencies and works with the Python standard library.

**Supported Python versions:**

* Python 3.8+
* Python 3.9+
* Python 3.10+
* Python 3.11+
* Python 3.12+

Installation Methods
--------------------

Install from Git Repository
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

The recommended way to install ConfigAt is directly from the Git repository:

.. code-block:: bash

   pip install git+https://github.com/Dog-Egg/configat.git

This will install the latest version from the main branch.

Install from Source
~~~~~~~~~~~~~~~~~~~

You can also clone the repository and install from source:

.. code-block:: bash

   git clone https://github.com/Dog-Egg/configat.git
   cd configat
   pip install .

Development Installation
~~~~~~~~~~~~~~~~~~~~~~~~

For development purposes, install in editable mode:

.. code-block:: bash

   git clone https://github.com/Dog-Egg/configat.git
   cd configat
   pip install -e .

This allows you to make changes to the source code and see them reflected immediately.

Verification
------------

After installation, verify that ConfigAt is working correctly:

.. code-block:: python

   import configat
   
   # Test basic functionality
   result = configat.resolve("test_value")
   print(f"ConfigAt is working: {result}")

You can also run a more comprehensive test:

.. code-block:: python

   import os
   import configat
   
   # Set a test environment variable
   os.environ["TEST_VAR"] = "Hello, ConfigAt!"
   
   # Load it using ConfigAt
   value = configat.resolve("@env:TEST_VAR")
   print(f"Loaded value: {value}")
   
   # Test type casting
   os.environ["TEST_NUMBER"] = "42"
   number = configat.resolve("@env:TEST_NUMBER", cast=int)
   print(f"Loaded number: {number} (type: {type(number)})")

Expected output:

.. code-block:: text

   ConfigAt is working: test_value
   Loaded value: Hello, ConfigAt!
   Loaded number: 42 (type: <class 'int'>)

Troubleshooting
---------------

Import Error
~~~~~~~~~~~~

If you encounter an import error:

.. code-block:: python

   ImportError: No module named 'configat'

Make sure you have installed ConfigAt correctly and that you're using the correct Python environment.

Version Check
~~~~~~~~~~~~~

To check the installed version:

.. code-block:: python

   import configat
   print(configat.__version__)  # If version info is available

Or check the package information:

.. code-block:: bash

   pip show configat

Virtual Environments
--------------------

It's recommended to install ConfigAt in a virtual environment to avoid conflicts with other packages:

Using venv
~~~~~~~~~~

.. code-block:: bash

   python -m venv configat_env
   source configat_env/bin/activate  # On Windows: configat_env\Scripts\activate
   pip install git+https://github.com/Dog-Egg/configat.git

Using conda
~~~~~~~~~~~

.. code-block:: bash

   conda create -n configat_env python=3.11
   conda activate configat_env
   pip install git+https://github.com/Dog-Egg/configat.git

Next Steps
----------

After successful installation, proceed to the :doc:`usage` guide to learn how to use ConfigAt in your projects.
