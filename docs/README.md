# ConfigAt Documentation

This directory contains the Sphinx documentation for ConfigAt.

## Building the Documentation

### Prerequisites

Install Sphinx and the required theme:

```bash
pip install sphinx sphinx-rtd-theme
```

### Building HTML Documentation

Using Make (Linux/macOS):

```bash
make html
```

Using Make (Windows):

```cmd
make.bat html
```

Using Sphinx directly:

```bash
sphinx-build -b html . _build/html
```

### Cleaning Build Files

```bash
make clean
```

### Viewing the Documentation

After building, open `_build/html/index.html` in your web browser.

## Documentation Structure

- `index.rst` - Main documentation index
- `installation.rst` - Installation guide
- `usage.rst` - Basic usage guide
- `loaders.rst` - Built-in loaders documentation
- `type_casting.rst` - Type casting system documentation
- `advanced.rst` - Advanced usage patterns
- `api/modules.rst` - API reference documentation
- `conf.py` - Sphinx configuration

## Contributing to Documentation

When adding new features to ConfigAt:

1. Update the relevant documentation files
2. Add examples and use cases
3. Update the API reference if needed
4. <PERSON>uild and test the documentation locally
5. Ensure all links work correctly

## Documentation Standards

- Use clear, concise English
- Provide practical examples for all features
- Include error handling examples
- Document all parameters and return values
- Use consistent formatting and style
