Usage Guide
===========

This guide covers the basic usage patterns and concepts of ConfigAt.

Basic Concepts
--------------

ConfigAt uses a simple syntax to load configuration values from various sources:

.. code-block:: text

   @<loader_name>[-loader_name...]:<loader_variable>

Where:

* ``@`` - Indicates the start of a loader expression
* ``loader_name`` - The name of the loader to use
* ``-loader_name`` - Optional additional loaders to chain (series loaders)
* ``:`` - Separates the loader specification from the variable
* ``loader_variable`` - The variable or path to load

Basic Usage
-----------

Import and Resolve
~~~~~~~~~~~~~~~~~~

.. code-block:: python

   import configat
   
   # Basic resolution
   value = configat.resolve("@env:MY_VARIABLE")

The ``resolve`` function is the main entry point for loading configuration values.

Environment Variables
~~~~~~~~~~~~~~~~~~~~~

Load values from environment variables:

.. code-block:: python

   import os
   import configat
   
   # Set an environment variable
   os.environ["DATABASE_URL"] = "postgresql://localhost:5432/mydb"
   
   # Load it using ConfigAt
   db_url = configat.resolve("@env:DATABASE_URL")
   print(db_url)  # Output: postgresql://localhost:5432/mydb

File Loading
~~~~~~~~~~~~

Load content from files:

.. code-block:: python

   import configat
   
   # Load content from a file
   content = configat.resolve("@file:/path/to/config.txt")

JSON Loading
~~~~~~~~~~~~

Parse JSON strings:

.. code-block:: python

   import configat
   
   # Load and parse JSON
   config = configat.resolve('@json:{"host": "localhost", "port": 8080}')
   print(config)  # Output: {'host': 'localhost', 'port': 8080}

Series Loaders
--------------

Chain multiple loaders together for complex transformations:

.. code-block:: python

   import configat
   
   # Load from file and strip whitespace
   clean_content = configat.resolve("@file-strip:/path/to/config.txt")
   
   # Load from environment, then from file, then strip
   # If MY_CONFIG_PATH contains "@file:/path/to/secret.txt "
   secret = configat.resolve("@env-strip:MY_CONFIG_PATH")

Common Series Loader Patterns:

* ``@file-strip`` - Load file content and remove whitespace
* ``@env-json`` - Load environment variable and parse as JSON
* ``@file-json`` - Load file content and parse as JSON
* ``@env-file-strip`` - Load path from env var, then load file, then strip

Default Values
--------------

Provide fallback values when the configuration is not found:

.. code-block:: python

   import configat
   
   # With default value
   port = configat.resolve("@env:PORT", default=8080)
   
   # If PORT environment variable is not set, returns 8080
   print(port)  # Output: 8080 (if PORT is not set)

Type Casting
------------

Automatically convert loaded values to specific types:

.. code-block:: python

   import configat
   
   # Cast to integer
   port = configat.resolve("@env:PORT", cast=int)
   
   # Cast to boolean using built-in boolean caster
   debug = configat.resolve("@env:DEBUG", cast=configat.casting.boolean)
   
   # Combine with default values
   timeout = configat.resolve("@env:TIMEOUT", default="30", cast=int)

Recursive Loading
-----------------

ConfigAt supports recursive loading, where the result of one loader can contain another loader expression:

.. code-block:: python

   import os
   import configat
   
   # Set up nested configuration
   os.environ["CONFIG_PATH"] = "@file:/path/to/config.txt"
   # Assume config.txt contains: @env:SECRET_KEY
   os.environ["SECRET_KEY"] = "my-secret-value"
   
   # This will resolve recursively:
   # 1. Load CONFIG_PATH from environment -> "@file:/path/to/config.txt"
   # 2. Load content from file -> "@env:SECRET_KEY"
   # 3. Load SECRET_KEY from environment -> "my-secret-value"
   secret = configat.resolve("@env:CONFIG_PATH")
   print(secret)  # Output: my-secret-value

Error Handling
--------------

ConfigAt raises specific exceptions for different error conditions:

.. code-block:: python

   import configat
   from configat.exceptions import NotFoundError
   
   try:
       value = configat.resolve("@env:NON_EXISTENT_VAR")
   except NotFoundError as e:
       print(f"Configuration not found: {e}")
   
   try:
       value = configat.resolve("@invalid_loader:something")
   except ValueError as e:
       print(f"Invalid loader: {e}")

Common Patterns
---------------

Configuration Class
~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   import configat
   
   class Config:
       def __init__(self):
           self.database_url = configat.resolve("@env:DATABASE_URL")
           self.debug = configat.resolve("@env:DEBUG", default="false", cast=configat.casting.boolean)
           self.port = configat.resolve("@env:PORT", default="8000", cast=int)
           self.secret_key = configat.resolve("@file-strip:@env:SECRET_KEY_FILE")
   
   config = Config()

Environment-Specific Configuration
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   import configat
   
   # Load different configs based on environment
   env = configat.resolve("@env:ENVIRONMENT", default="development")
   config_file = f"@file:config/{env}.json"
   config = configat.resolve(config_file, cast=configat.resolve)

Docker Secrets Pattern
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   import configat
   
   # Load secrets from Docker secrets or environment variables
   db_password = configat.resolve("@file-strip:/run/secrets/db_password", 
                                  default="@env:DB_PASSWORD")

Next Steps
----------

* Learn about :doc:`loaders` for detailed information about each loader
* Explore :doc:`type_casting` for advanced type conversion
* Check :doc:`advanced` for custom loaders and advanced patterns
