Type Casting
=============

ConfigAt provides a flexible type casting system that allows you to automatically convert loaded configuration values to specific Python types. This is particularly useful when loading values from environment variables or files, which are typically strings.

Basic Type Casting
-------------------

Use the ``cast`` parameter in the ``resolve`` function to specify the target type:

.. code-block:: python

   import os
   import configat
   
   # Set up test data
   os.environ["PORT"] = "8080"
   os.environ["DEBUG"] = "true"
   os.environ["TIMEOUT"] = "30.5"
   
   # Cast to different types
   port = configat.resolve("@env:PORT", cast=int)
   debug = configat.resolve("@env:DEBUG", cast=configat.casting.boolean)
   timeout = configat.resolve("@env:TIMEOUT", cast=float)
   
   print(f"Port: {port} (type: {type(port)})")        # Port: 8080 (type: <class 'int'>)
   print(f"Debug: {debug} (type: {type(debug)})")     # Debug: True (type: <class 'bool'>)
   print(f"Timeout: {timeout} (type: {type(timeout)})") # Timeout: 30.5 (type: <class 'float'>)

Built-in Python Types
----------------------

You can use any built-in Python type as a caster:

Integer Casting
~~~~~~~~~~~~~~~

.. code-block:: python

   import configat
   
   # Cast to integer
   port = configat.resolve("@env:PORT", cast=int)
   workers = configat.resolve("@env:WORKERS", default="4", cast=int)

Float Casting
~~~~~~~~~~~~~

.. code-block:: python

   import configat
   
   # Cast to float
   timeout = configat.resolve("@env:TIMEOUT", cast=float)
   rate_limit = configat.resolve("@env:RATE_LIMIT", default="1.5", cast=float)

String Casting
~~~~~~~~~~~~~~

.. code-block:: python

   import configat
   
   # Explicit string casting (usually not needed)
   name = configat.resolve("@env:APP_NAME", cast=str)

List Casting
~~~~~~~~~~~~

.. code-block:: python

   import configat
   
   # Cast JSON array to list
   items = configat.resolve('@json:["a", "b", "c"]', cast=list)
   print(items)  # Output: ['a', 'b', 'c']

Built-in ConfigAt Casters
-------------------------

Boolean Caster
~~~~~~~~~~~~~~

ConfigAt provides a flexible boolean caster that recognizes multiple representations:

.. code-block:: python

   import configat
   
   # All of these resolve to True
   configat.resolve("@env:FLAG", default="True", cast=configat.casting.boolean)
   configat.resolve("@env:FLAG", default="true", cast=configat.casting.boolean)
   configat.resolve("@env:FLAG", default="1", cast=configat.casting.boolean)
   configat.resolve("@env:FLAG", default="Yes", cast=configat.casting.boolean)
   configat.resolve("@env:FLAG", default="yes", cast=configat.casting.boolean)
   
   # All of these resolve to False
   configat.resolve("@env:FLAG", default="False", cast=configat.casting.boolean)
   configat.resolve("@env:FLAG", default="false", cast=configat.casting.boolean)
   configat.resolve("@env:FLAG", default="0", cast=configat.casting.boolean)
   configat.resolve("@env:FLAG", default="No", cast=configat.casting.boolean)
   configat.resolve("@env:FLAG", default="no", cast=configat.casting.boolean)

**Supported Truth Values:**

* "True", "true"
* "1", 1
* "Yes", "yes"

**Supported False Values:**

* "False", "false"
* "0", 0
* "No", "no"

**Error Handling:**

.. code-block:: python

   import configat
   
   try:
       value = configat.resolve("@env:INVALID_BOOL", default="maybe", cast=configat.casting.boolean)
   except ValueError as e:
       print(f"Invalid boolean value: {e}")

Custom Casters
--------------

You can create custom casting functions for complex data types:

Simple Custom Caster
~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   import configat
   
   def to_uppercase(value):
       return str(value).upper()
   
   # Use custom caster
   name = configat.resolve("@env:APP_NAME", default="myapp", cast=to_uppercase)
   print(name)  # Output: MYAPP

List Caster
~~~~~~~~~~~

.. code-block:: python

   import configat
   
   def comma_separated_list(value):
       return [item.strip() for item in str(value).split(',')]
   
   # Parse comma-separated values
   hosts = configat.resolve("@env:HOSTS", default="localhost,127.0.0.1", cast=comma_separated_list)
   print(hosts)  # Output: ['localhost', '127.0.0.1']

URL Parsing Caster
~~~~~~~~~~~~~~~~~~

.. code-block:: python

   import configat
   from urllib.parse import urlparse
   
   def parse_url(value):
       parsed = urlparse(str(value))
       return {
           'scheme': parsed.scheme,
           'host': parsed.hostname,
           'port': parsed.port,
           'path': parsed.path
       }
   
   # Parse database URL
   db_config = configat.resolve("@env:DATABASE_URL", 
                               default="postgresql://localhost:5432/mydb",
                               cast=parse_url)
   print(db_config)  # Output: {'scheme': 'postgresql', 'host': 'localhost', ...}

Complex Object Caster
~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   import configat
   import json
   from dataclasses import dataclass
   
   @dataclass
   class DatabaseConfig:
       host: str
       port: int
       database: str
       username: str
       password: str
   
   def to_db_config(value):
       if isinstance(value, str):
           data = json.loads(value)
       else:
           data = value
       return DatabaseConfig(**data)
   
   # Load complex configuration
   db_config = configat.resolve("@env-json:DB_CONFIG", cast=to_db_config)

Combining with Default Values
-----------------------------

Type casting works seamlessly with default values:

.. code-block:: python

   import configat
   
   # Default value is cast to the target type
   port = configat.resolve("@env:PORT", default="8080", cast=int)
   debug = configat.resolve("@env:DEBUG", default="false", cast=configat.casting.boolean)
   
   # Both the loaded value and default value go through the caster
   timeout = configat.resolve("@env:TIMEOUT", default="30", cast=float)

Error Handling
--------------

Type casting can raise exceptions if the conversion fails:

.. code-block:: python

   import configat
   
   try:
       # This will raise ValueError if PORT contains non-numeric characters
       port = configat.resolve("@env:PORT", default="invalid", cast=int)
   except ValueError as e:
       print(f"Cannot convert to integer: {e}")
   
   try:
       # This will raise ValueError for invalid boolean values
       debug = configat.resolve("@env:DEBUG", default="maybe", cast=configat.casting.boolean)
   except ValueError as e:
       print(f"Cannot convert to boolean: {e}")

Best Practices
--------------

1. **Use appropriate types**: Choose the right type for your configuration values
2. **Provide sensible defaults**: Ensure default values can be successfully cast
3. **Handle casting errors**: Wrap casting operations in try-catch blocks when needed
4. **Document expected formats**: Make it clear what format values should be in
5. **Test edge cases**: Verify your casters work with various input formats

Common Patterns
---------------

Configuration Class with Casting
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   import configat
   
   class AppConfig:
       def __init__(self):
           self.host = configat.resolve("@env:HOST", default="localhost")
           self.port = configat.resolve("@env:PORT", default="8000", cast=int)
           self.debug = configat.resolve("@env:DEBUG", default="false", cast=configat.casting.boolean)
           self.workers = configat.resolve("@env:WORKERS", default="4", cast=int)
           self.timeout = configat.resolve("@env:TIMEOUT", default="30.0", cast=float)
   
   config = AppConfig()

Environment-Specific Casting
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   import configat
   
   def safe_int_cast(value, fallback=0):
       try:
           return int(value)
       except (ValueError, TypeError):
           return fallback
   
   # Safe casting with fallback
   port = configat.resolve("@env:PORT", default="8000", cast=lambda x: safe_int_cast(x, 8000))

Next Steps
----------

* Explore :doc:`advanced` for more complex configuration patterns
* Check the :doc:`api/modules` for detailed API documentation
* Learn about custom loaders in the advanced guide
