Advanced Usage
==============

This guide covers advanced ConfigAt features including custom loaders, complex configuration patterns, error handling strategies, and best practices for production use.

Custom Loaders
--------------

Creating Custom Loaders
~~~~~~~~~~~~~~~~~~~~~~~

You can extend ConfigAt by creating custom loaders for specific data sources or transformation needs:

.. code-block:: python

   from configat.main import ConfigAt
   import requests
   import base64
   
   # Create a custom ConfigAt instance
   config = ConfigAt()
   
   # Add built-in loaders
   from configat import loaders
   config.add_loader("env", loaders.env_loader)
   config.add_loader("file", loaders.file_loader)
   config.add_loader("strip", loaders.strip_loader)
   config.add_loader("json", loaders.json_loader)
   
   # Add custom loaders
   def http_loader(url):
       """Load content from HTTP URL"""
       response = requests.get(url)
       response.raise_for_status()
       return response.text
   
   def base64_loader(data):
       """Decode base64 data"""
       return base64.b64decode(data).decode('utf-8')
   
   config.add_loader("http", http_loader)
   config.add_loader("base64", base64_loader)
   
   # Use custom loaders
   content = config.resolve("@http:https://api.example.com/config")
   secret = config.resolve("@base64:bXlfc2VjcmV0X2tleQ==")

Database Loader Example
~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   import sqlite3
   from configat.main import ConfigAt
   
   def sqlite_loader(query):
       """Load value from SQLite database"""
       # Format: "database.db:SELECT value FROM config WHERE key='mykey'"
       db_path, sql = query.split(':', 1)
       
       conn = sqlite3.connect(db_path)
       try:
           cursor = conn.execute(sql)
           result = cursor.fetchone()
           return result[0] if result else None
       finally:
           conn.close()
   
   config = ConfigAt()
   config.add_loader("sqlite", sqlite_loader)
   
   # Usage
   value = config.resolve("@sqlite:config.db:SELECT value FROM settings WHERE key='api_key'")

Consul/etcd Loader Example
~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   import requests
   from configat.main import ConfigAt
   
   def consul_loader(key):
       """Load value from Consul KV store"""
       response = requests.get(f"http://localhost:8500/v1/kv/{key}")
       if response.status_code == 404:
           raise NotFoundError(f"Consul key not found: {key}")
       response.raise_for_status()
       
       data = response.json()[0]
       import base64
       return base64.b64decode(data['Value']).decode('utf-8')
   
   config = ConfigAt()
   config.add_loader("consul", consul_loader)
   
   # Usage
   db_url = config.resolve("@consul:database/url")

Complex Configuration Patterns
------------------------------

Hierarchical Configuration
~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   import configat
   import os
   
   class HierarchicalConfig:
       def __init__(self, env="development"):
           self.env = env
           
           # Load base configuration
           self.base_config = self._load_config("base")
           
           # Load environment-specific configuration
           self.env_config = self._load_config(env)
           
           # Merge configurations
           self.config = {**self.base_config, **self.env_config}
       
       def _load_config(self, name):
           try:
               return configat.resolve(f"@file-json:config/{name}.json")
           except:
               return {}
       
       def get(self, key, default=None):
           return self.config.get(key, default)
   
   # Usage
   config = HierarchicalConfig(os.environ.get("ENVIRONMENT", "development"))
   db_url = config.get("database_url")

Configuration Templates
~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   import configat
   import string
   
   def template_loader(template_str):
       """Load configuration with template substitution"""
       template = string.Template(template_str)
       
       # Get all environment variables for substitution
       import os
       return template.safe_substitute(os.environ)
   
   config = ConfigAt()
   config.add_loader("template", template_loader)
   
   # Usage with template
   # If DATABASE_HOST=localhost and DATABASE_PORT=5432
   db_url = config.resolve("@template:postgresql://$DATABASE_HOST:$DATABASE_PORT/mydb")

Conditional Configuration
~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   import configat
   import os
   
   def conditional_loader(condition_value):
       """Load different values based on condition"""
       condition, true_value, false_value = condition_value.split('|', 2)
       
       # Evaluate condition (simple example)
       if condition.startswith("env:"):
           env_var = condition[4:]
           condition_result = os.environ.get(env_var, "").lower() in ("true", "1", "yes")
       else:
           condition_result = bool(condition)
       
       return true_value if condition_result else false_value
   
   config = ConfigAt()
   config.add_loader("if", conditional_loader)
   
   # Usage
   # Load different database based on DEBUG environment variable
   db_url = config.resolve("@if:env:DEBUG|sqlite:///dev.db|postgresql://prod-server/db")

Error Handling Strategies
-------------------------

Graceful Degradation
~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   import configat
   from configat.exceptions import NotFoundError
   
   def safe_resolve(expr, fallback=None, cast=None):
       """Safely resolve configuration with fallback"""
       try:
           return configat.resolve(expr, cast=cast)
       except (NotFoundError, ValueError) as e:
           print(f"Configuration warning: {e}")
           return fallback
   
   # Usage
   api_key = safe_resolve("@env:API_KEY", fallback="development-key")
   port = safe_resolve("@env:PORT", fallback=8000, cast=int)

Configuration Validation
~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   import configat
   from typing import Dict, Any
   
   class ValidatedConfig:
       def __init__(self, schema: Dict[str, Any]):
           self.schema = schema
           self.config = {}
           self._load_and_validate()
       
       def _load_and_validate(self):
           for key, spec in self.schema.items():
               expr = spec['expr']
               cast_func = spec.get('cast')
               default = spec.get('default')
               required = spec.get('required', False)
               
               try:
                   value = configat.resolve(expr, default=default, cast=cast_func)
                   
                   # Custom validation
                   if 'validate' in spec:
                       if not spec['validate'](value):
                           raise ValueError(f"Validation failed for {key}: {value}")
                   
                   self.config[key] = value
               except Exception as e:
                   if required:
                       raise ValueError(f"Required configuration {key} failed: {e}")
                   else:
                       print(f"Optional configuration {key} failed: {e}")
       
       def get(self, key):
           return self.config.get(key)
   
   # Usage
   schema = {
       'database_url': {
           'expr': '@env:DATABASE_URL',
           'required': True
       },
       'port': {
           'expr': '@env:PORT',
           'default': '8000',
           'cast': int,
           'validate': lambda x: 1 <= x <= 65535
       },
       'debug': {
           'expr': '@env:DEBUG',
           'default': 'false',
           'cast': configat.casting.boolean
       }
   }
   
   config = ValidatedConfig(schema)

Retry and Circuit Breaker
~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   import configat
   import time
   from functools import wraps
   
   def retry_loader(max_retries=3, delay=1):
       """Decorator to add retry logic to loaders"""
       def decorator(loader_func):
           @wraps(loader_func)
           def wrapper(value):
               last_exception = None
               for attempt in range(max_retries):
                   try:
                       return loader_func(value)
                   except Exception as e:
                       last_exception = e
                       if attempt < max_retries - 1:
                           time.sleep(delay * (2 ** attempt))  # Exponential backoff
               raise last_exception
           return wrapper
       return decorator
   
   # Apply to custom loader
   @retry_loader(max_retries=3, delay=0.5)
   def unreliable_http_loader(url):
       import requests
       response = requests.get(url, timeout=5)
       response.raise_for_status()
       return response.text

Performance Optimization
------------------------

Caching Configuration
~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   import configat
   from functools import lru_cache
   import time
   
   class CachedConfig:
       def __init__(self, ttl=300):  # 5 minutes TTL
           self.ttl = ttl
           self._cache = {}
           self._timestamps = {}
       
       def resolve(self, expr, **kwargs):
           now = time.time()
           
           # Check if cached value is still valid
           if expr in self._cache:
               if now - self._timestamps[expr] < self.ttl:
                   return self._cache[expr]
           
           # Load fresh value
           value = configat.resolve(expr, **kwargs)
           
           # Cache the result
           self._cache[expr] = value
           self._timestamps[expr] = now
           
           return value
   
   # Usage
   cached_config = CachedConfig(ttl=600)  # 10 minutes cache
   value = cached_config.resolve("@env:EXPENSIVE_TO_LOAD")

Lazy Loading
~~~~~~~~~~~~

.. code-block:: python

   import configat
   
   class LazyConfig:
       def __init__(self):
           self._loaded = {}
           self._expressions = {}
       
       def register(self, name, expr, **kwargs):
           """Register a configuration without loading it"""
           self._expressions[name] = (expr, kwargs)
       
       def get(self, name):
           """Load configuration on first access"""
           if name not in self._loaded:
               if name not in self._expressions:
                   raise KeyError(f"Configuration {name} not registered")
               
               expr, kwargs = self._expressions[name]
               self._loaded[name] = configat.resolve(expr, **kwargs)
           
           return self._loaded[name]
   
   # Usage
   config = LazyConfig()
   config.register("database_url", "@env:DATABASE_URL")
   config.register("port", "@env:PORT", default="8000", cast=int)
   
   # Values are loaded only when accessed
   db_url = config.get("database_url")

Best Practices
--------------

1. **Environment-Specific Configuration**

   .. code-block:: python

      import configat
      import os
      
      env = os.environ.get("ENVIRONMENT", "development")
      
      # Load environment-specific configuration
      config_file = f"@file-json:config/{env}.json"
      config = configat.resolve(config_file, default={})

2. **Secret Management**

   .. code-block:: python

      import configat
      
      # Prefer file-based secrets over environment variables
      api_key = configat.resolve("@file-strip:/run/secrets/api_key",
                                 default="@env:API_KEY")

3. **Configuration Documentation**

   .. code-block:: python

      """
      Configuration Schema:
      
      Required:
      - DATABASE_URL: @env:DATABASE_URL - PostgreSQL connection string
      - SECRET_KEY: @file-strip:/run/secrets/secret_key - Application secret
      
      Optional:
      - PORT: @env:PORT (default: 8000) - Server port
      - DEBUG: @env:DEBUG (default: false) - Debug mode
      - WORKERS: @env:WORKERS (default: 4) - Number of worker processes
      """

4. **Testing Configuration**

   .. code-block:: python

      import configat
      import os
      import tempfile
      
      def test_config_loading():
          # Create temporary config file
          with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
              f.write('{"test": "value"}')
              temp_path = f.name
          
          try:
              # Test file loading
              config = configat.resolve(f"@file-json:{temp_path}")
              assert config["test"] == "value"
          finally:
              os.unlink(temp_path)

5. **Error Reporting**

   .. code-block:: python

      import configat
      import logging
      
      logger = logging.getLogger(__name__)
      
      def load_config_with_logging():
          try:
              return configat.resolve("@env:CRITICAL_CONFIG")
          except Exception as e:
              logger.error(f"Failed to load critical configuration: {e}")
              raise

Migration and Compatibility
---------------------------

When migrating from other configuration systems:

.. code-block:: python

   import configat
   import os
   
   # Gradual migration helper
   def migrate_config(old_key, new_expr, cast=None):
       """Helper to migrate from old environment variables to ConfigAt"""
       # Try new ConfigAt expression first
       try:
           return configat.resolve(new_expr, cast=cast)
       except:
           # Fall back to old environment variable
           old_value = os.environ.get(old_key)
           if old_value is not None:
               return cast(old_value) if cast else old_value
           raise
   
   # Usage during migration
   port = migrate_config("OLD_PORT", "@env:NEW_PORT", cast=int)

Next Steps
----------

* Review the complete :doc:`api/modules` for all available functions and classes
* Check out the source code examples in the repository
* Consider contributing custom loaders back to the project
